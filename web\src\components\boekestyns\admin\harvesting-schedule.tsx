import { useMemo, useRef, useState, Fragment } from 'react';
import { useDrop } from 'react-dnd';
import * as HeadlessUI from '@headlessui/react';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { ProblemDetails } from '@/utils/problem-details';
import {
  selectSchedules,
  ScheduleHarvestingOrderType,
} from './harvesting-slice';
import { HarvestingScheduleWorkOrder } from './harvesting-schedule-work-order';
import { useAddHarvestingOrderToHarvestingScheduleMutation } from 'api/boekestyn-harvesting-service';
import moment from 'moment';
import { HarvestingWorkOrderDay } from './harvesting-work-order-day';
import {
  setSelectedOrder,
  updateWorkOrderData,
  addWorkOrderDate,
  removeWorkOrderDate,
  setSelectedTab,
  setShowDatePicker,
  setWorkOrderDate,
  resetWorkOrders,
  selectSelectedOrder,
  selectWorkOrdersByDate,
  selectSelectedTab,
  selectShowDatePicker,
  selectWorkOrderDate,
  selectWorkOrderDates,
} from './harvesting-work-orders-slice';

interface HarvestingScheduleProps {
  line: models.HarvestingLine;
  date: string;
}

export function HarvestingSchedule({ line, date }: HarvestingScheduleProps) {
  const dispatch = useAppDispatch(),
    [error, setError] = useState<ProblemDetails | null>(null),
    schedules = useAppSelector(selectSchedules),
    selectedOrder = useAppSelector(selectSelectedOrder),
    workOrdersByDate = useAppSelector(selectWorkOrdersByDate),
    selectedTab = useAppSelector(selectSelectedTab),
    showDatePicker = useAppSelector(selectShowDatePicker),
    workOrderDate = useAppSelector(selectWorkOrderDate),
    workOrderDates = useAppSelector(selectWorkOrderDates),
    [addOrderToSchedule] = useAddHarvestingOrderToHarvestingScheduleMutation(),
    schedule = useMemo(
      () =>
        schedules.find((s) => s.lineId === line.id && s.date === date) ?? {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        },
      [schedules, line.id, date]
    ),
    totalPots = useMemo(
      () => schedule.workOrders.reduce((total, o) => total + o.pots, 0) ?? 0,
      [schedule]
    ),
    estimatedHours = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.estimatedHours, 0) ??
        0,
      [schedule]
    ),
    [{ isOver }, drop] = useDrop<
      models.HarvestingOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: ScheduleHarvestingOrderType,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(order: models.HarvestingOrder) {
        setError(null);
        dispatch(setSelectedOrder(order));
      },
    })),
    ref = useRef<HTMLDivElement>(null);

  const handleDetailConfirm = async () => {
    if (selectedOrder) {
      console.log(workOrdersByDate);

      // Get sorted dates to identify the last date
      const sortedDates = Object.keys(workOrdersByDate).sort();
      const lastDate = sortedDates[sortedDates.length - 1];

      // Update the slice state to set finalRound: true for the last date
      if (lastDate && !workOrdersByDate[lastDate].finalRound) {
        dispatch(updateWorkOrderData({ date: lastDate, updates: { finalRound: true } }));
      }

      const workOrders = Object.keys(workOrdersByDate).map((date) => {
        const schedule = {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        };

        // Set finalRound to true for the last date, otherwise use the existing value
        const isLastDate = date === lastDate;
        const finalRound = isLastDate ? true : (workOrdersByDate[date].finalRound || false);

        return {
          crewSize: workOrdersByDate[date].crewSize || 1,
          comments: workOrdersByDate[date].harvestingComments || null,
          schedule,
          order: selectedOrder,
          varieties: workOrdersByDate[date].varieties || [],
          maximumHarvestRounds: workOrdersByDate[date].maximumHarvestRounds || 1,
          defaultExpectedHarvestPercentage: workOrdersByDate[date].defaultExpectedHarvestPercentage || 0,
          finalRound,
        };
      });

      addOrderToSchedule(workOrders);

      dispatch(resetWorkOrders());
    }
  };

  drop(ref);
  // Function to update work order data
  const handleUpdateWorkOrderData = (date: string, updates: Partial<models.HarvestingWorkOrder>) => {
    dispatch(updateWorkOrderData({ date, updates }));
  };

  function handleAddWorkOrderDate(): void {
    // Only add if the date doesn't already exist
    if (!workOrdersByDate.hasOwnProperty(workOrderDate)) {
      dispatch(addWorkOrderDate(workOrderDate));
    }
    dispatch(setShowDatePicker(false));
  }

  function handleRemoveWorkOrderDate(date: string): void {
    dispatch(removeWorkOrderDate(date));
  }

  return (
    <div
      ref={ref}
      className={classNames(
        'm-2 rounded border p-2',
        isOver && 'border-green-600'
      )}
    >
      <h2 className="text-lg font-semibold">{line.name}</h2>
      <div className="flex-grow">
        {!!schedule.id && (
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr className="sticky top-0 z-10">
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Lot #
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Size / Plant
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Harvesting
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Pots
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  &nbsp;
                </th>
              </tr>
            </thead>
            <tbody>
              {schedule.workOrders.map((order) => (
                <HarvestingScheduleWorkOrder
                  key={order.id}
                  order={order}
                  scheduleId={schedule.id}
                />
              ))}
            </tbody>
            <tfoot>
              <tr>
                <th colSpan={4} className="p-2 text-right">
                  Total Hours:
                </th>
                <th className="p-2 text-right">{formatNumber(totalPots)}</th>
                <th className="p-2 text-right">
                  {formatNumber(estimatedHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
              </tr>
            </tfoot>
          </table>
        )}
        {!schedule.id && (
          <div className="flex h-24 items-center justify-center text-gray-500">
            <span className="text-sm italic">Drag to schedule orders.</span>
          </div>
        )}
      </div>
      <HeadlessUI.Transition.Root
        show={!!selectedOrder}
        as={Fragment}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30 max-w-5xl"
          onClose={() => dispatch(setSelectedOrder(null))}
          open={!!selectedOrder}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <HeadlessUI.Tab.Group selectedIndex={selectedTab} onChange={(index) => dispatch(setSelectedTab(index))}>
                    <HeadlessUI.Tab.List
                      as="nav"
                      className="flex w-full flex-row overflow-x-auto border-b"
                    >
                     {workOrderDates.map((date) => (
                      <HeadlessUI.Tab key={date}>
                        {({ selected }) => (
                          <div className="flex items-center">
                            <button
                              type="button"
                              className={classNames(
                                'whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                                selected
                                  ? 'border-blue-500 text-blue-600'
                                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                              )}
                            >
                              {moment(date).format('ddd')}
                              <div className="text-xs italic">
                                {moment(date).format('MMM D')}
                              </div>
                            </button>
                            <button
                              type="button"
                              className="btn-secondary ml-2 border-none shadow-none p-1"
                              onClick={() => handleRemoveWorkOrderDate(date!)}
                            >
                              <Icon icon="x" className="text-red-500" />
                            </button>
                          </div>
                        )}
                      </HeadlessUI.Tab>
                     ))}
                     <button
                        type="button"
                        className="btn-secondary"
                        onClick={() => dispatch(setShowDatePicker(true))}
                      >
                        <Icon icon="plus" />
                      </button>
                    </HeadlessUI.Tab.List>

                    {/*showDatePicker && (
                      <div className="mt-4 border-t pt-4">
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-500">
                              Add Date
                            </label>
                            <input
                              type="date"
                              value={workOrderDate}
                              onChange={(e) => dispatch(setWorkOrderDate(e.target.value))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                          <div className="flex gap-2">
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={() => dispatch(setShowDatePicker(false))}
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              className="btn-primary"
                              onClick={handleAddWorkOrderDate}
                            >
                              Add
                            </button>
                          </div>
                        </div>
                      </div>
                    )*/}

                    <HeadlessUI.Tab.Panels
                      as="div"
                      className="flex flex-grow flex-col overflow-y-auto border-t"
                    >
                      {workOrderDates.map((date) => (
                        <HeadlessUI.Tab.Panel
                          key={date}
                          className="mt-4"
                        >
                          <HarvestingWorkOrderDay
                            key={date}
                            workOrder={workOrdersByDate[date]}
                            allWorkOrders={workOrdersByDate}
                            order={selectedOrder}
                            date={date}
                            onUpdateWorkOrder={handleUpdateWorkOrderData}
                          />
                        </HeadlessUI.Tab.Panel>
                      ))}
                    </HeadlessUI.Tab.Panels>
                  </HeadlessUI.Tab.Group>
                  <button type="button" className="btn-primary ml-2" onClick={handleDetailConfirm}>
                    Save
                  </button>
                  <button type="button" className="btn-secondary ml-2" onClick={() => dispatch(setSelectedOrder(null))}>
                    Cancel
                  </button>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>

      <HeadlessUI.Transition.Root
        show={showDatePicker}
        as={Fragment}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30 max-w-5xl"
          onClose={() => dispatch(setShowDatePicker(false))}
          open={showDatePicker}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={(e) => e.stopPropagation()} />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto" onClick={(e) => e.stopPropagation()}>
            <div className="flex min-h-full items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-sm transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <div className="flex flex-col">
                    <div className="flex flex-col">
                      <label className="block text-sm font-medium text-gray-500">
                        Add Date
                      </label>
                      <input
                        type="date"
                        value={workOrderDate}
                        onChange={(e) => dispatch(setWorkOrderDate(e.target.value))}
                        onClick={(e) => e.stopPropagation()}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div className="mt-4 text-right">
                      <button
                        type="button"
                        className="btn-secondary"
                        onClick={() => dispatch(setShowDatePicker(false))}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary ml-2"
                        onClick={handleAddWorkOrderDate}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>

    </div>
  );
}
