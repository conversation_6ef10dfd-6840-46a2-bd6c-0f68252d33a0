import { useState, useEffect } from 'react';
import { useDebounce } from 'use-debounce';
import { useHarvestingOrdersQuery } from 'api/boekestyn-harvesting-service';
import { Icon } from '@/components/icon';
import {
  selectStartDate,
  selectEndDate,
  setStartDate,
  setEndDate,
  nextWeek,
  previousWeek,
} from '@/components/boekestyns/admin/admin-slice';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { HarvestingOrderRow } from './harvesting-order';
import {
  selectOrders,
  selectSort,
  selectSortDescending,
  setSort,
  SortFields,
  selectPlants,
  selectPlant,
  setPlant,
} from './harvesting-slice';

interface HeaderButtonProps {
  text: string;
  propName: SortFields;
}

export function HarvestingOrders() {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    orders = useAppSelector(selectOrders),
    sort = useAppSelector(selectSort),
    sortDescending = useAppSelector(selectSortDescending),
    plant = useAppSelector(selectPlant),
    plants = useAppSelector(selectPlants),
    [start, setStart] = useState(startDate),
    [end, setEnd] = useState(endDate),
    [startDateDebounced, { flush: flushStart, isPending: startPending }] =
      useDebounce(start, 500),
    [endDateDebounced, { flush: flushEnd, isPending: endPending }] =
      useDebounce(end, 500);

  useHarvestingOrdersQuery({
    startDate: startDateDebounced,
    endDate: endDateDebounced,
  });

  useEffect(() => {
    if (startDateDebounced) {
      dispatch(setStartDate(startDateDebounced));
    }
  }, [startDateDebounced, dispatch]);

  useEffect(() => {
    if (endDateDebounced) {
      dispatch(setEndDate(endDateDebounced));
    }
  }, [endDateDebounced, dispatch]);

  useEffect(() => {
    if (startDate) {
      setStart(startDate);
      flushStart();
    }
  }, [startDate, flushStart]);

  useEffect(() => {
    if (endDate) {
      setEnd(endDate);
      flushEnd();
    }
  }, [endDate, flushEnd]);

  const handleStartDateChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setStart(event.target.value);
  };

  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEnd(event.target.value);
  };

  const handleNextWeek = () => {
    dispatch(nextWeek());
  };

  const handlePreviousWeek = () => {
    dispatch(previousWeek());
  };

  const handlePlantChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setPlant(event.target.value));
  };

  const handleColumnSort = (sortProp: SortFields) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setSort({ sort: sortProp, sortDescending: descending }));
  };

  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      <div>{text}</div>
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <div className="flex flex-grow flex-col overflow-y-auto">
      <div className="mb-2 grid grid-cols-3 gap-4 border-b p-2">
        <div>
          <label className="block text-sm font-medium text-gray-500">
            From
          </label>
          <input
            type="date"
            max="2050-01-01"
            value={startDate}
            onChange={handleStartDateChange}
            className="text-xs"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-500">To</label>
          <input
            type="date"
            max="2050-01-01"
            value={endDate}
            onChange={handleEndDateChange}
            className="text-xs"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-500">
            Plant
          </label>
          <select
            value={plant ?? ''}
            onChange={handlePlantChange}
            className="block w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">All Plants</option>
            {plants.map((plant) => (
              <option key={plant._id} value={plant._id}>
                {plant.name}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div className="mb-2 flex-grow overflow-y-auto px-2">
        <table className="min-w-full divide-y divide-gray-300 text-sm">
          <thead>
            <tr className="sticky top-0 z-10">
              <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                <HeaderButton text="Harvest Date" propName="flowerDate" />
              </th>
              <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                <HeaderButton text="Lot #" propName="orderNumber" />
              </th>
              <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                <HeaderButton text="Size" propName="size" />
                &nbsp;/&nbsp;
                <HeaderButton text="Plant" propName="crop" />
              </th>
              <th className="bg-gray-100 p-2">&nbsp;</th>
              <th className="bg-gray-100 p-2 text-right">
                <HeaderButton text="Pots" propName="pots" />
              </th>
              <th className="bg-gray-100 p-2 text-right">
                <HeaderButton text="Harvested" propName="pots" />
              </th>
              <th className="bg-gray-100 p-2 text-right">
                <HeaderButton text="Thrown&nbsp;Out" propName="pots" />
              </th>
              <th className="bg-gray-100 p-2 text-right">
                <HeaderButton text="Remaining" propName="pots" />
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-300">
            {orders.map((order) => (
              <HarvestingOrderRow key={order._id} order={order} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
